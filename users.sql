DROP DATABASE IF EXISTS users;
CREATE DATABASE users;
USE users;

DROP TABLE IF EXISTS users;
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL
);

INSERT INTO users (name, email, password, role) VALUES
    ('<PERSON><PERSON><PERSON>', '<EMAIL>', '123456', 'patient'),
    ('<PERSON><PERSON>', '<EMAIL>', '123456', 'patient'),
    ('<PERSON><PERSON><PERSON>', '<EMAIL>', '123456', 'doctor'),
    ('<PERSON><PERSON><PERSON>', '<EMAIL>', '123456', 'doctor'),
    ('<PERSON><PERSON>', '<EMAIL>', '123456', 'admin');
