{% extends 'base.html' %}

{% block content %}



<style>
  .background-image {
    background-image: url('static/homebg.jpg');
    height: 100vh;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    text-shadow: 2px 2px #333;
  }
</style>

<div class="background-image">

  <h1>Welcome to my Web Page</h1>
  <p>Here's some content for the page</p>
  <nav class="navbar navbar-expand-lg navbar-dark bg-trasparant mt-5">
    <ul class="navbar-nav">
      <li class="nav-item mr-5">
        <a class="nav-link" href="{{ url_for('login') }}">Log In</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="{{url_for('register')}}">Sign Up</a>
      </li>
    </ul>
  </nav>
</div>
{% if messages %}
<div class="row justify-content-center pt-3">
  <div class="col-md-6">
    {% for message in messages %}
    <div class="alert alert-primary" role="alert">{{message}}</div>
    {% endfor %}
  </div>
  {% endif %}

  {% endblock content %}