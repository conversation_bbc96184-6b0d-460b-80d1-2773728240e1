-- Database OMS (Outlet Management System)
DROP DATABASE IF EXISTS OMS;
CREATE DATABASE OMS;
USE OMS;

-- Categories Table
DROP TABLE IF EXISTS Categories;
CREATE TABLE Categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_date DATE DEFAULT (CURRENT_DATE)
);

INSERT INTO Categories (category_name, description) VALUES
('Electronics', 'Electronic devices and accessories'),
('Clothing', 'Apparel and fashion items'),
('Home & Garden', 'Home improvement and gardening supplies'),
('Sports & Outdoors', 'Sports equipment and outdoor gear'),
('Books & Media', 'Books, magazines, and media content'),
('Health & Beauty', 'Health and beauty products'),
('Toys & Games', 'Toys and gaming products'),
('Food & Beverages', 'Food items and beverages'),
('Automotive', 'Car accessories and automotive supplies'),
('Office Supplies', 'Office and stationery items');

-- Suppliers Table
DROP TABLE IF EXISTS Suppliers;
CREATE TABLE Suppliers (
    supplier_id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'India',
    payment_terms VARCHAR(100),
    created_date DATE DEFAULT (CURRENT_DATE)
);

INSERT INTO Suppliers (supplier_name, contact_person, email, phone, address, city, state, postal_code, payment_terms) VALUES
('TechWorld Distributors', 'Rajesh Kumar', '<EMAIL>', '**********', '123 Tech Street', 'Mumbai', 'Maharashtra', '400001', 'Net 30'),
('Fashion Hub Suppliers', 'Priya Sharma', '<EMAIL>', '9876543211', '456 Fashion Ave', 'Delhi', 'Delhi', '110001', 'Net 15'),
('Home Essentials Ltd', 'Amit Patel', '<EMAIL>', '9876543212', '789 Home Road', 'Bangalore', 'Karnataka', '560001', 'Net 45'),
('Sports Zone Wholesale', 'Neha Singh', '<EMAIL>', '9876543213', '321 Sports Complex', 'Pune', 'Maharashtra', '411001', 'Net 30'),
('Book Paradise Distributors', 'Vikram Gupta', '<EMAIL>', '9876543214', '654 Book Street', 'Chennai', 'Tamil Nadu', '600001', 'Net 30'),
('Beauty World Suppliers', 'Sunita Rao', '<EMAIL>', '9876543215', '987 Beauty Lane', 'Hyderabad', 'Telangana', '500001', 'Net 15'),
('Toy Kingdom Wholesale', 'Ravi Mehta', '<EMAIL>', '9876543216', '147 Toy Street', 'Kolkata', 'West Bengal', '700001', 'Net 30'),
('Fresh Foods Distributors', 'Kavita Joshi', '<EMAIL>', '9876543217', '258 Food Market', 'Ahmedabad', 'Gujarat', '380001', 'Net 7'),
('Auto Parts Central', 'Deepak Verma', '<EMAIL>', '9876543218', '369 Auto Street', 'Jaipur', 'Rajasthan', '302001', 'Net 30'),
('Office Solutions Inc', 'Meera Agarwal', '<EMAIL>', '9876543219', '741 Office Plaza', 'Lucknow', 'Uttar Pradesh', '226001', 'Net 30');

-- Products Table
DROP TABLE IF EXISTS Products;
CREATE TABLE Products (
    product_id INT PRIMARY KEY AUTO_INCREMENT,
    product_name VARCHAR(200) NOT NULL,
    category_id INT,
    supplier_id INT,
    sku VARCHAR(50) UNIQUE,
    barcode VARCHAR(100),
    description TEXT,
    unit_price DECIMAL(10,2) NOT NULL,
    cost_price DECIMAL(10,2) NOT NULL,
    weight DECIMAL(8,3),
    dimensions VARCHAR(50),
    brand VARCHAR(100),
    model VARCHAR(100),
    color VARCHAR(50),
    size VARCHAR(50),
    warranty_period VARCHAR(50),
    created_date DATE DEFAULT (CURRENT_DATE),
    updated_date DATE DEFAULT (CURRENT_DATE),
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (category_id) REFERENCES Categories(category_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES Suppliers(supplier_id) ON DELETE SET NULL ON UPDATE CASCADE
);

INSERT INTO Products (product_name, category_id, supplier_id, sku, barcode, description, unit_price, cost_price, brand, model, color, warranty_period) VALUES
('Samsung Galaxy Smartphone', 1, 1, 'ELEC001', '1234567890123', 'Latest Samsung Galaxy smartphone with advanced features', 25000.00, 20000.00, 'Samsung', 'Galaxy S23', 'Black', '1 Year'),
('Nike Running Shoes', 2, 2, 'CLOTH001', '2345678901234', 'Comfortable running shoes for daily exercise', 3500.00, 2500.00, 'Nike', 'Air Max', 'White', '6 Months'),
('LED Table Lamp', 3, 3, 'HOME001', '3456789012345', 'Energy efficient LED table lamp for study', 1200.00, 800.00, 'Philips', 'LED Pro', 'Silver', '2 Years'),
('Cricket Bat', 4, 4, 'SPORT001', '4567890123456', 'Professional cricket bat made from English willow', 2500.00, 1800.00, 'MRF', 'Genius', 'Brown', '1 Year'),
('Programming Book', 5, 5, 'BOOK001', '5678901234567', 'Complete guide to Python programming', 800.00, 600.00, 'Tech Publications', 'Python Guide', 'Multi', 'N/A'),
('Face Cream', 6, 6, 'BEAUTY001', '6789012345678', 'Anti-aging face cream with natural ingredients', 1500.00, 1000.00, 'Olay', 'Regenerist', 'White', 'N/A'),
('LEGO Building Set', 7, 7, 'TOY001', '7890123456789', 'Creative building set for children aged 6+', 2000.00, 1400.00, 'LEGO', 'Creator', 'Multi', '1 Year'),
('Organic Tea', 8, 8, 'FOOD001', '8901234567890', 'Premium organic green tea leaves', 300.00, 200.00, 'Twinings', 'Green Tea', 'Green', 'N/A'),
('Car Phone Holder', 9, 9, 'AUTO001', '9012345678901', 'Universal car phone holder with 360-degree rotation', 500.00, 300.00, 'Portronics', 'Car Mount', 'Black', '6 Months'),
('Notebook Set', 10, 10, 'OFFICE001', '0123456789012', 'Set of 5 ruled notebooks for office use', 250.00, 150.00, 'Classmate', 'Premium', 'Blue', 'N/A');

-- Inventory Table
DROP TABLE IF EXISTS Inventory;
CREATE TABLE Inventory (
    inventory_id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    quantity_in_stock INT NOT NULL DEFAULT 0,
    minimum_stock_level INT DEFAULT 10,
    maximum_stock_level INT DEFAULT 1000,
    reorder_point INT DEFAULT 20,
    last_restocked_date DATE,
    location VARCHAR(100),
    status ENUM('Available', 'Low Stock', 'Out of Stock', 'Discontinued') DEFAULT 'Available',
    FOREIGN KEY (product_id) REFERENCES Products(product_id) ON DELETE CASCADE ON UPDATE CASCADE
);

INSERT INTO Inventory (product_id, quantity_in_stock, minimum_stock_level, reorder_point, last_restocked_date, location) VALUES
(1, 50, 5, 10, '2024-01-15', 'A1-01'),
(2, 75, 10, 20, '2024-01-20', 'B2-05'),
(3, 100, 15, 25, '2024-01-18', 'C3-02'),
(4, 30, 5, 10, '2024-01-22', 'D4-01'),
(5, 200, 20, 40, '2024-01-25', 'E5-03'),
(6, 80, 10, 20, '2024-01-19', 'F6-02'),
(7, 45, 8, 15, '2024-01-21', 'G7-01'),
(8, 150, 25, 50, '2024-01-23', 'H8-04'),
(9, 60, 10, 20, '2024-01-24', 'I9-01'),
(10, 300, 50, 100, '2024-01-26', 'J10-02');

-- Customers Table
DROP TABLE IF EXISTS Customers;
CREATE TABLE Customers (
    customer_id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'India',
    customer_type ENUM('Regular', 'Premium', 'VIP') DEFAULT 'Regular',
    loyalty_points INT DEFAULT 0,
    total_purchases DECIMAL(12,2) DEFAULT 0.00,
    registration_date DATE DEFAULT (CURRENT_DATE),
    last_purchase_date DATE,
    is_active BOOLEAN DEFAULT TRUE
);

INSERT INTO Customers (first_name, last_name, email, phone, date_of_birth, gender, address, city, state, postal_code, customer_type, loyalty_points, total_purchases, last_purchase_date) VALUES
('Rahul', 'Sharma', '<EMAIL>', '**********', '1990-05-15', 'Male', '123 MG Road', 'Mumbai', 'Maharashtra', '400001', 'Premium', 1250, 45000.00, '2024-01-25'),
('Priya', 'Patel', '<EMAIL>', '9876543211', '1985-08-22', 'Female', '456 Park Street', 'Delhi', 'Delhi', '110001', 'Regular', 800, 28000.00, '2024-01-20'),
('Amit', 'Kumar', '<EMAIL>', '9876543212', '1992-12-10', 'Male', '789 Brigade Road', 'Bangalore', 'Karnataka', '560001', 'VIP', 2100, 75000.00, '2024-01-28'),
('Sneha', 'Singh', '<EMAIL>', '9876543213', '1988-03-18', 'Female', '321 FC Road', 'Pune', 'Maharashtra', '411001', 'Regular', 450, 15000.00, '2024-01-15'),
('Vikram', 'Gupta', '<EMAIL>', '9876543214', '1995-07-25', 'Male', '654 Anna Salai', 'Chennai', 'Tamil Nadu', '600001', 'Premium', 950, 32000.00, '2024-01-22');

-- Staff/Employees Table
DROP TABLE IF EXISTS Staff;
CREATE TABLE Staff (
    staff_id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    postal_code VARCHAR(20),
    position VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    salary DECIMAL(10,2),
    hire_date DATE NOT NULL,
    employment_status ENUM('Active', 'Inactive', 'Terminated') DEFAULT 'Active',
    manager_id INT,
    shift_timing VARCHAR(50),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_date DATE DEFAULT (CURRENT_DATE),
    FOREIGN KEY (manager_id) REFERENCES Staff(staff_id) ON DELETE SET NULL ON UPDATE CASCADE
);

INSERT INTO Staff (employee_id, first_name, last_name, email, phone, date_of_birth, gender, address, city, state, postal_code, position, department, salary, hire_date, shift_timing, emergency_contact_name, emergency_contact_phone) VALUES
('EMP001', 'Rajesh', 'Kumar', '<EMAIL>', '9876543220', '1985-04-12', 'Male', '123 Staff Colony', 'Mumbai', 'Maharashtra', '400001', 'Store Manager', 'Management', 50000.00, '2020-01-15', '9 AM - 6 PM', 'Sunita Kumar', '9876543221'),
('EMP002', 'Priya', 'Sharma', '<EMAIL>', '9876543222', '1990-06-20', 'Female', '456 Residential Area', 'Mumbai', 'Maharashtra', '400002', 'Sales Associate', 'Sales', 25000.00, '2021-03-10', '10 AM - 7 PM', 'Rohit Sharma', '9876543223'),
('EMP003', 'Amit', 'Patel', '<EMAIL>', '9876543224', '1988-09-15', 'Male', '789 Housing Society', 'Mumbai', 'Maharashtra', '400003', 'Inventory Manager', 'Inventory', 35000.00, '2020-08-05', '8 AM - 5 PM', 'Neha Patel', '9876543225'),
('EMP004', 'Neha', 'Singh', '<EMAIL>', '9876543226', '1992-11-30', 'Female', '321 Apartment Complex', 'Mumbai', 'Maharashtra', '400004', 'Sales Associate', 'Sales', 22000.00, '2022-01-20', '2 PM - 11 PM', 'Vikram Singh', '9876543227'),
('EMP005', 'Vikram', 'Gupta', '<EMAIL>', '9876543228', '1987-02-28', 'Male', '654 Staff Quarters', 'Mumbai', 'Maharashtra', '400005', 'Cashier', 'Sales', 20000.00, '2021-07-12', '9 AM - 6 PM', 'Kavita Gupta', '**********');

-- Sales Transactions Table
DROP TABLE IF EXISTS Sales;
CREATE TABLE Sales (
    sale_id INT PRIMARY KEY AUTO_INCREMENT,
    sale_date DATE NOT NULL,
    sale_time TIME NOT NULL,
    customer_id INT,
    staff_id INT NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('Cash', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking') NOT NULL,
    payment_status ENUM('Paid', 'Pending', 'Refunded') DEFAULT 'Paid',
    transaction_reference VARCHAR(100),
    notes TEXT,
    created_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES Customers(customer_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (staff_id) REFERENCES Staff(staff_id) ON DELETE RESTRICT ON UPDATE CASCADE
);

INSERT INTO Sales (sale_date, sale_time, customer_id, staff_id, subtotal, tax_amount, discount_amount, total_amount, payment_method, transaction_reference) VALUES
('2024-01-25', '14:30:00', 1, 2, 25000.00, 4500.00, 1000.00, 28500.00, 'Credit Card', 'TXN001'),
('2024-01-25', '15:45:00', 2, 4, 3500.00, 630.00, 0.00, 4130.00, 'UPI', 'TXN002'),
('2024-01-26', '11:20:00', 3, 2, 1200.00, 216.00, 50.00, 1366.00, 'Cash', 'TXN003'),
('2024-01-26', '16:10:00', 4, 5, 2500.00, 450.00, 100.00, 2850.00, 'Debit Card', 'TXN004'),
('2024-01-27', '13:25:00', 5, 4, 800.00, 144.00, 0.00, 944.00, 'UPI', 'TXN005');

-- Sale Items Table (Junction table for Sales and Products)
DROP TABLE IF EXISTS Sale_Items;
CREATE TABLE Sale_Items (
    sale_item_id INT PRIMARY KEY AUTO_INCREMENT,
    sale_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    line_total DECIMAL(10,2) NOT NULL,
    discount_per_item DECIMAL(10,2) DEFAULT 0.00,
    FOREIGN KEY (sale_id) REFERENCES Sales(sale_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (product_id) REFERENCES Products(product_id) ON DELETE RESTRICT ON UPDATE CASCADE
);

INSERT INTO Sale_Items (sale_id, product_id, quantity, unit_price, line_total) VALUES
(1, 1, 1, 25000.00, 25000.00),
(2, 2, 1, 3500.00, 3500.00),
(3, 3, 1, 1200.00, 1200.00),
(4, 4, 1, 2500.00, 2500.00),
(5, 5, 1, 800.00, 800.00);

-- Purchase Orders Table
DROP TABLE IF EXISTS Purchase_Orders;
CREATE TABLE Purchase_Orders (
    order_id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INT NOT NULL,
    staff_id INT NOT NULL,
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    status ENUM('Pending', 'Confirmed', 'Shipped', 'Delivered', 'Cancelled') DEFAULT 'Pending',
    subtotal DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_cost DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    payment_terms VARCHAR(100),
    notes TEXT,
    created_date DATE DEFAULT (CURRENT_DATE),
    FOREIGN KEY (supplier_id) REFERENCES Suppliers(supplier_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (staff_id) REFERENCES Staff(staff_id) ON DELETE RESTRICT ON UPDATE CASCADE
);

INSERT INTO Purchase_Orders (order_number, supplier_id, staff_id, order_date, expected_delivery_date, status, subtotal, tax_amount, total_amount, payment_terms) VALUES
('PO001', 1, 3, '2024-01-15', '2024-01-25', 'Delivered', 100000.00, 18000.00, 118000.00, 'Net 30'),
('PO002', 2, 3, '2024-01-18', '2024-01-28', 'Delivered', 50000.00, 9000.00, 59000.00, 'Net 15'),
('PO003', 3, 3, '2024-01-20', '2024-01-30', 'Shipped', 25000.00, 4500.00, 29500.00, 'Net 45'),
('PO004', 4, 3, '2024-01-22', '2024-02-01', 'Confirmed', 30000.00, 5400.00, 35400.00, 'Net 30'),
('PO005', 5, 3, '2024-01-25', '2024-02-05', 'Pending', 15000.00, 2700.00, 17700.00, 'Net 30');

-- Purchase Order Items Table
DROP TABLE IF EXISTS Purchase_Order_Items;
CREATE TABLE Purchase_Order_Items (
    po_item_id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity_ordered INT NOT NULL,
    quantity_received INT DEFAULT 0,
    unit_cost DECIMAL(10,2) NOT NULL,
    line_total DECIMAL(12,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES Purchase_Orders(order_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (product_id) REFERENCES Products(product_id) ON DELETE RESTRICT ON UPDATE CASCADE
);

INSERT INTO Purchase_Order_Items (order_id, product_id, quantity_ordered, quantity_received, unit_cost, line_total) VALUES
(1, 1, 5, 5, 20000.00, 100000.00),
(2, 2, 20, 20, 2500.00, 50000.00),
(3, 3, 30, 0, 800.00, 24000.00),
(4, 4, 15, 0, 1800.00, 27000.00),
(5, 5, 25, 0, 600.00, 15000.00);

-- Users Table for Authentication
DROP TABLE IF EXISTS Users;
CREATE TABLE Users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager', 'sales_staff', 'inventory_staff') NOT NULL,
    staff_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_date DATE DEFAULT (CURRENT_DATE),
    FOREIGN KEY (staff_id) REFERENCES Staff(staff_id) ON DELETE SET NULL ON UPDATE CASCADE
);

INSERT INTO Users (username, email, password, role, staff_id) VALUES
('admin', '<EMAIL>', '123456', 'admin', 1),
('manager', '<EMAIL>', '123456', 'manager', 1),
('sales1', '<EMAIL>', '123456', 'sales_staff', 2),
('sales2', '<EMAIL>', '123456', 'sales_staff', 4),
('inventory1', '<EMAIL>', '123456', 'inventory_staff', 3);

-- Create Views for Better Data Access
CREATE VIEW Product_Inventory_View AS
SELECT
    p.product_id,
    p.product_name,
    p.sku,
    p.unit_price,
    p.cost_price,
    c.category_name,
    s.supplier_name,
    i.quantity_in_stock,
    i.minimum_stock_level,
    i.reorder_point,
    i.status as inventory_status,
    i.location,
    p.brand,
    p.model,
    p.is_active
FROM Products p
LEFT JOIN Categories c ON p.category_id = c.category_id
LEFT JOIN Suppliers s ON p.supplier_id = s.supplier_id
LEFT JOIN Inventory i ON p.product_id = i.product_id;

CREATE VIEW Sales_Summary_View AS
SELECT
    s.sale_id,
    s.sale_date,
    s.sale_time,
    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
    c.phone as customer_phone,
    CONCAT(st.first_name, ' ', st.last_name) as staff_name,
    s.subtotal,
    s.tax_amount,
    s.discount_amount,
    s.total_amount,
    s.payment_method,
    s.payment_status
FROM Sales s
LEFT JOIN Customers c ON s.customer_id = c.customer_id
LEFT JOIN Staff st ON s.staff_id = st.staff_id;

CREATE VIEW Low_Stock_Alert_View AS
SELECT
    p.product_id,
    p.product_name,
    p.sku,
    i.quantity_in_stock,
    i.minimum_stock_level,
    i.reorder_point,
    c.category_name,
    s.supplier_name,
    s.phone as supplier_phone
FROM Products p
JOIN Inventory i ON p.product_id = i.product_id
LEFT JOIN Categories c ON p.category_id = c.category_id
LEFT JOIN Suppliers s ON p.supplier_id = s.supplier_id
WHERE i.quantity_in_stock <= i.reorder_point;

CREATE VIEW Customer_Purchase_History_View AS
SELECT
    c.customer_id,
    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
    c.email,
    c.phone,
    c.customer_type,
    c.loyalty_points,
    c.total_purchases,
    s.sale_id,
    s.sale_date,
    s.total_amount as purchase_amount,
    s.payment_method
FROM Customers c
LEFT JOIN Sales s ON c.customer_id = s.customer_id
ORDER BY c.customer_id, s.sale_date DESC;

CREATE VIEW Staff_Performance_View AS
SELECT
    st.staff_id,
    st.employee_id,
    CONCAT(st.first_name, ' ', st.last_name) as staff_name,
    st.position,
    st.department,
    COUNT(s.sale_id) as total_sales,
    COALESCE(SUM(s.total_amount), 0) as total_sales_amount,
    AVG(s.total_amount) as average_sale_amount
FROM Staff st
LEFT JOIN Sales s ON st.staff_id = s.staff_id
WHERE st.employment_status = 'Active'
GROUP BY st.staff_id, st.employee_id, st.first_name, st.last_name, st.position, st.department;

-- Create Indexes for Better Performance
CREATE INDEX idx_products_category ON Products(category_id);
CREATE INDEX idx_products_supplier ON Products(supplier_id);
CREATE INDEX idx_products_sku ON Products(sku);
CREATE INDEX idx_products_name ON Products(product_name);
CREATE INDEX idx_products_brand ON Products(brand);
CREATE INDEX idx_inventory_product ON Inventory(product_id);
CREATE INDEX idx_inventory_status ON Inventory(status);
CREATE INDEX idx_customers_email ON Customers(email);
CREATE INDEX idx_customers_phone ON Customers(phone);
CREATE INDEX idx_customers_type ON Customers(customer_type);
CREATE INDEX idx_staff_employee_id ON Staff(employee_id);
CREATE INDEX idx_staff_position ON Staff(position);
CREATE INDEX idx_staff_department ON Staff(department);
CREATE INDEX idx_sales_date ON Sales(sale_date);
CREATE INDEX idx_sales_customer ON Sales(customer_id);
CREATE INDEX idx_sales_staff ON Sales(staff_id);
CREATE INDEX idx_sales_payment_method ON Sales(payment_method);
CREATE INDEX idx_sale_items_sale ON Sale_Items(sale_id);
CREATE INDEX idx_sale_items_product ON Sale_Items(product_id);
CREATE INDEX idx_purchase_orders_supplier ON Purchase_Orders(supplier_id);
CREATE INDEX idx_purchase_orders_staff ON Purchase_Orders(staff_id);
CREATE INDEX idx_purchase_orders_date ON Purchase_Orders(order_date);
CREATE INDEX idx_purchase_orders_status ON Purchase_Orders(status);
CREATE INDEX idx_po_items_order ON Purchase_Order_Items(order_id);
CREATE INDEX idx_po_items_product ON Purchase_Order_Items(product_id);
CREATE INDEX idx_users_username ON Users(username);
CREATE INDEX idx_users_email ON Users(email);
CREATE INDEX idx_users_role ON Users(role);

-- Create Full-Text Indexes for Search Functionality
CREATE FULLTEXT INDEX ft_products_search ON Products(product_name, description);
CREATE FULLTEXT INDEX ft_customers_search ON Customers(first_name, last_name);
CREATE FULLTEXT INDEX ft_suppliers_search ON Suppliers(supplier_name, contact_person);

-- Add Triggers for Automatic Updates
DELIMITER //

-- Trigger to update inventory when sale is made
CREATE TRIGGER update_inventory_after_sale
AFTER INSERT ON Sale_Items
FOR EACH ROW
BEGIN
    UPDATE Inventory
    SET quantity_in_stock = quantity_in_stock - NEW.quantity
    WHERE product_id = NEW.product_id;

    -- Update inventory status based on stock level
    UPDATE Inventory
    SET status = CASE
        WHEN quantity_in_stock = 0 THEN 'Out of Stock'
        WHEN quantity_in_stock <= reorder_point THEN 'Low Stock'
        ELSE 'Available'
    END
    WHERE product_id = NEW.product_id;
END//

-- Trigger to update customer total purchases
CREATE TRIGGER update_customer_purchases
AFTER INSERT ON Sales
FOR EACH ROW
BEGIN
    UPDATE Customers
    SET total_purchases = total_purchases + NEW.total_amount,
        last_purchase_date = NEW.sale_date,
        loyalty_points = loyalty_points + FLOOR(NEW.total_amount / 100)
    WHERE customer_id = NEW.customer_id;
END//

-- Trigger to update inventory when purchase order items are received
CREATE TRIGGER update_inventory_after_purchase
AFTER UPDATE ON Purchase_Order_Items
FOR EACH ROW
BEGIN
    IF NEW.quantity_received > OLD.quantity_received THEN
        UPDATE Inventory
        SET quantity_in_stock = quantity_in_stock + (NEW.quantity_received - OLD.quantity_received),
            last_restocked_date = CURDATE()
        WHERE product_id = NEW.product_id;

        -- Update inventory status
        UPDATE Inventory
        SET status = CASE
            WHEN quantity_in_stock = 0 THEN 'Out of Stock'
            WHEN quantity_in_stock <= reorder_point THEN 'Low Stock'
            ELSE 'Available'
        END
        WHERE product_id = NEW.product_id;
    END IF;
END//

DELIMITER ;

-- Insert additional sample data for testing
INSERT INTO Customers (first_name, last_name, email, phone, date_of_birth, gender, address, city, state, postal_code, customer_type, loyalty_points, total_purchases, last_purchase_date) VALUES
('Anita', 'Desai', '<EMAIL>', '9876543215', '1993-01-12', 'Female', '987 Lake View', 'Hyderabad', 'Telangana', '500001', 'Regular', 300, 12000.00, '2024-01-18'),
('Ravi', 'Mehta', '<EMAIL>', '9876543216', '1989-09-05', 'Male', '147 Garden Street', 'Kolkata', 'West Bengal', '700001', 'Premium', 750, 25000.00, '2024-01-24'),
('Kavita', 'Joshi', '<EMAIL>', '9876543217', '1991-11-20', 'Female', '258 Market Road', 'Ahmedabad', 'Gujarat', '380001', 'Regular', 200, 8000.00, '2024-01-16'),
('Deepak', 'Verma', '<EMAIL>', '9876543218', '1986-04-30', 'Male', '369 Industrial Area', 'Jaipur', 'Rajasthan', '302001', 'VIP', 1800, 65000.00, '2024-01-26'),
('Meera', 'Agarwal', '<EMAIL>', '9876543219', '1994-06-14', 'Female', '741 Business District', 'Lucknow', 'Uttar Pradesh', '226001', 'Regular', 150, 5000.00, '2024-01-12');

-- COMMIT all changes
COMMIT;
