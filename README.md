# 🛍️ **Full-Stack Outlet Management Web Application using Flask and MySQL**

## **Problem Statement: Development of a Full-Stack Outlet Management System**

This project aims to develop a robust, full-stack web application for managing the daily operations of a retail **outlet** using **Flask (Python)** for the back-end and **MySQL** as the relational database. The development team is divided into two groups:

* **Group G1**: Responsible for front-end design and user interaction
* **Group G2**: Responsible for back-end logic and database integration

The application supports dynamic, secure, and efficient execution of core outlet management functions, including product management, sales tracking, inventory control, and staff administration. The system implements full **CRUD operations**, supports conditional data retrieval via **WHERE clauses**, and provides functionality for field and record updates or deletions.

An essential feature of the application is **role-based user authentication**, ensuring secure access for different users such as:

* **Managers/Admins** - Full system access
* **Sales Staff** - Customer and sales management
* **Inventory Staff** - Product and inventory management

### 💾 **Core Features:**

1. **Database Schema Design** - Well-structured relational schema managing products, sales, inventory, customers, staff, and suppliers
2. **Back-End Development** - Flask-based API endpoints with secure MySQL integration
3. **Front-End Development** - Responsive UI using HTML, CSS, and Bootstrap
4. **User Authentication** - Role-based access control with session management
5. **CRUD Operations** - Complete Create, Read, Update, Delete functionality
6. **Search & Filter** - Advanced search capabilities with WHERE clause support
7. **Inventory Management** - Real-time stock tracking and low-stock alerts
8. **Sales Management** - Transaction processing and sales reporting
9. **Customer Management** - Customer profiles and purchase history
10. **Supplier Management** - Vendor information and purchase orders

## 🚀 **Installation & Setup**

### **Prerequisites**
- Python 3.7 or higher
- MySQL Server 8.0 or higher
- Git

### **Installation Steps**

1. **Clone the repository:**
```bash
git clone <repository-url>
cd Database_Project-main
```

2. **Create virtual environment:**
```bash
python3 -m venv venv
```

3. **Activate virtual environment:**
```bash
# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

4. **Install required packages:**
```bash
pip install flask
pip install flask-mysqldb
pip install pyyaml
```

5. **Setup MySQL Database:**
   - Install MySQL Server and MySQL Workbench
   - Create a new database connection
   - Import the `outlet_management_system.sql` file
   - Update database credentials in `app.py`

6. **Configure Database Connection:**
   Update the following in `app.py`:
   ```python
   app.config['MYSQL_HOST'] = '127.0.0.1'
   app.config['MYSQL_USER'] = 'your_username'
   app.config['MYSQL_PASSWORD'] = 'your_password'
   app.config['MYSQL_DB'] = 'OMS'
   ```

7. **Run the application:**
```bash
python app.py
```

8. **Access the application:**
   Open your browser and navigate to `http://localhost:7000`

## 📋 **Default Login Credentials**

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | 123456 |
| Manager | <EMAIL> | 123456 |
| Sales Staff | <EMAIL> | 123456 |
| Inventory Staff | <EMAIL> | 123456 |

## 🗂️ **Database Schema Overview**

The outlet management system includes the following main tables:

- **Categories** - Product categories
- **Suppliers** - Vendor information
- **Products** - Product catalog with pricing and details
- **Inventory** - Stock levels and location tracking
- **Customers** - Customer profiles and loyalty information
- **Staff** - Employee information and roles
- **Sales** - Transaction records
- **Sale_Items** - Individual items in each sale
- **Purchase_Orders** - Orders placed with suppliers
- **Users** - Authentication and role management

## 🔧 **Key Features & Functionality**

### **Role-Based Access Control**

- **Admin**: Full system access including user management
- **Manager**: Access to all operational data and reports
- **Sales Staff**: Customer management, sales processing, product viewing
- **Inventory Staff**: Product management, inventory control, supplier management

### **CRUD Operations**

- **Create**: Add new products, customers, sales, purchase orders
- **Read**: View and search all data with advanced filtering
- **Update**: Modify existing records with validation
- **Delete**: Remove records with proper constraint handling

### **Advanced Features**

- **Real-time Inventory Tracking**: Automatic stock updates on sales
- **Low Stock Alerts**: Automated notifications for reorder points
- **Customer Loyalty System**: Points-based rewards program
- **Sales Analytics**: Performance tracking and reporting
- **Supplier Management**: Purchase order processing and tracking
- **Search & Filter**: Advanced search with WHERE clause support

## 🖥️ **Application Usage**

### **Getting Started**

1. **Login**: Use the provided credentials to access the system
2. **Navigate**: Use the navigation bar to access different sections
3. **Manage Data**: Use the "Edit" section to perform CRUD operations
4. **Search**: Use search functionality to find specific records
5. **Logout**: Always logout when finished for security

### **Main Navigation**

- **Home**: Dashboard and system overview
- **Edit**: Access to all tables and CRUD operations
- **About Us**: Information about the system
- **Contact Us**: Support and contact information
- **Logout**: Secure session termination

## 🏗️ **Technical Architecture**

### **Backend Technologies**
- **Flask**: Python web framework for API development
- **MySQL**: Relational database management system
- **MySQLdb**: Python MySQL database connector
- **Session Management**: Secure user authentication

### **Frontend Technologies**
- **HTML5**: Semantic markup structure
- **CSS3**: Modern styling and responsive design
- **Bootstrap**: UI component framework
- **DaisyUI**: Additional UI components
- **JavaScript**: Interactive functionality

### **Database Features**
- **Triggers**: Automatic inventory updates
- **Views**: Simplified data access
- **Indexes**: Optimized query performance
- **Constraints**: Data integrity enforcement
- **Full-text Search**: Advanced search capabilities

## 📁 **Project Structure**

```
Database_Project-main/
├── app.py                          # Main Flask application
├── outlet_management_system.sql    # Database schema and sample data
├── sql_tools.py                    # Database utility functions
├── html_tools.py                   # HTML generation utilities
├── requirements.txt                # Python dependencies
├── static/                         # Static assets (CSS, JS, images)
├── templates/                      # HTML templates
│   ├── login.html                  # Login page
│   ├── register.html               # Registration page
│   ├── index.html                  # Home page
│   ├── pick_table.html             # Table selection
│   ├── edit.html                   # CRUD operations
│   ├── about_us.html               # About page
│   └── contact_us.html             # Contact page
└── snapshots/                      # Application screenshots
```

## 🔒 **Security Features**

- **Password Protection**: Secure user authentication
- **Session Management**: Secure session handling
- **Role-Based Access**: Granular permission control
- **SQL Injection Prevention**: Parameterized queries
- **Input Validation**: Server-side data validation

## 🚀 **Future Enhancements**

- **Dashboard Analytics**: Advanced reporting and charts
- **Email Notifications**: Automated alerts and notifications
- **Barcode Scanning**: Product identification system
- **Mobile App**: Companion mobile application
- **API Integration**: Third-party service integration
- **Multi-location Support**: Multiple outlet management

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 **Support**

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Developed with ❤️ for modern retail outlet management**
