<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- bootstrap link  -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
    crossorigin="anonymous"></script>
  <link href="https://cdn.jsdelivr.net/npm/daisyui@2.12.0/dist/full.css" rel="stylesheet" type="text/css" />


  <!-- font awesome   -->
  <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@700&display=swap" rel="stylesheet">

  <title>Dispensary Management System</title>

  <style>
    /* Styles for the page */

    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Merriweather', serif;
    }

    .container {
      max-width: 960px;
      margin: 0 auto;
      padding: 20px;
    }

    h1,
    h2 {
      text-align: center;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      border: 1px solid black;
      padding: 10px;
      text-align: center;
    }

    th {
      background-color: #ccc;
    }

    footer {
      color: #fff;
      padding: 20px 0;
    }

    .container-1 {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left,
    .center,
    .right {
      flex: 1;
      text-align: center;
    }

    .left {
      text-align: left;
    }

    .right {
      text-align: right;
    }

    .right a {
      color: #fff;
      margin-left: 10px;
    }

    .right a:first-child {
      margin-left: 0;
    }

    p,h1,
    h2 {
      line-height: 2;
    }

    .ghost-btn {
      color: black;
      background-color: transparent;
      border-radius: 0.25rem;
      padding: 0.5rem 1rem;
      transition: all 0.2s ease-in-out;
    }

    .ghost-btn:hover {
      color: white;
      background-color: #377cfb;
      border-color: #377cfb;
    }
  </style>
</head>

<body style="background-image: url(static/contact.jpg); height: 100%; width: 100%; overflow-x: hidden; font-family: 'Merriweather', serif;;">

  <!-- navbar -->
 {% include 'nav_bar.html' %}
  <!-- navbar end  -->

<div class="container my-5">
    <div class="row">
        <div class="col-md-6 mx-auto">
            <h1 class="text-center mb-4">Contact Us</h1>
            <form>
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" class="form-control" id="name" placeholder="Enter your name">
                </div>
                <br>
                <div class="form-group">
                    <label for="email">Email address</label>
                    <input type="email" class="form-control" id="email" aria-describedby="emailHelp"
                           placeholder="Enter email">
                    <small id="emailHelp" class="form-text text-muted">We'll never share your email with anyone
                        else.</small>
                </div>
                <br>
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea class="form-control" id="message" rows="5"></textarea>
                </div>
                <br>
                <button type="submit" class="btn btn-primary btn-lg" onclick="sendEmail(event)">Submit</button>
            </form>
        </div>
    </div>
</div>

<script>
    function sendEmail(event) {
        event.preventDefault(); // prevent the form from submitting normally

        // get the user's email address and message
        const email = document.getElementById("email").value;
        const message = document.getElementById("message").value;

        // copy the message to the user's clipboard
        navigator.clipboard.writeText(message)
            .then(() => {
                // open the user's default email client with a pre-filled email
                window.location.href = `mailto:${email}?subject=Contact%20Form%20Submission&body=${message}`;
            })
            .catch(error => {
                console.error(`Failed to copy message: ${error}`);
            });
    }
</script>





<!-- footer -->
{% include 'footer.html' %}
</body>
</html>