<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
    crossorigin="anonymous"></script>

  <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@700&display=swap" rel="stylesheet">

  <title>Dispensary Management System</title>
  <style>
    /* Styles for the page */
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Merriweather', serif;
    }

    .container {
      max-width: 960px;
      margin: 0 auto;
      padding: 20px;
    }

    h1,
    h2 {
      text-align: center;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      border: 1px solid black;
      padding: 10px;
      text-align: center;
    }

    th {
      background-color: #ccc;
    }

    footer {
      /* background-color: #333; */
      color: #fff;
      padding: 20px 0;
    }

    .container-1 {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left,
    .center,
    .right {
      flex: 1;
      text-align: center;
    }

    .left {
      text-align: left;
    }

    .right {
      text-align: right;
    }

    .right a {
      color: #fff;
      margin-left: 10px;
    }

    .right a:first-child {
      margin-left: 0;
    }
  </style>
</head>

<body style="background-image: url(static/bg1.jpg);">

  <div class="container" id="about">
    <h1>About</h1>
    <p>Our database management system is designed to store, manage, and access data
       efficiently. With a user-friendly interface and advanced features, 
       it allows easy record creation, editing, deletion, report generation, and data trend 
       analysis. Highly customizable, it can be tailored to specific needs. 
       Our system helps streamline operations for small business owners, researchers,
        and students. We offer reliable, secure, and scalable solutions.
    </p>
    <h2>Team</h2>
    <table>
      <thead>
        <tr>
          <th>G1</th>
          <th>G2</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Amaan Ansari</td>
          <td>Akshat Shrivastava</td>
        </tr>
        <tr>
          <td>Govardhan Ingale</td>
          <td>Dhakad Bhagat Singh</td>
        </tr>
        <tr>
          <td>Aishwarya Omar</td>
          <td>Mahesh Dange</td>
        </tr>
        <tr>
          <td>Ronak Hingonia</td>
          <td>Yash Kokane</td>
        </tr>
        <tr>
          <td>Ojas Washimkar</td>
          <td>Shubham Kumar</td>
        </tr>
        <tr>
          <td>----------</td>
          <td>Yash Adhiya</td>
        </tr>
      </tbody>
    </table>
    <h2>Motivation</h2>
    <p>We believe that data is the foundation of progress and innovation. 
      Our passion is to create tools that empower individuals and organizations
       to unlock their data's full potential. We aim to design cutting-edge 
       solutions to address modern data challenges and opportunities. 
       We strive to stay ahead of emerging trends and technologies to provide
        easy and accessible database management for everyone. Our mission is 
        to make database management efficient and intuitive. Join us on our journey
         towards a more data-driven world.</p>
  </div>

  <div class="container-fluid" style="font-size: small;">
    <footer class="py-3 my-4">
      <p class="text-center text-muted">&copy; The Database Dynamos IIT GN</p>
      <br>
      <p class="text-center text-muted">CS 432: Databases Project</p>
    </footer>
  </div>

</body>

</html>